
<a name="_toc296255960"></a><a name="_toc327852950"></a><a name="_toc359946392"></a><a name="_toc296723289"></a><a name="_toc296423708"></a><a name="_toc360453642"></a><a name="_toc422865759"></a><a name="_toc359946601"></a><a name="_toc297035845"></a><a name="_toc296256683"></a>文档编号：**JOU-SECD-JS4241-03T-01**

版 本 号：**V1.0**

`  `<a name="_toc517770658"></a><a name="_toc517672473"></a>基于JavaWeb的学生成绩管理系统项目开发计划说明书

|**项目名称**|`   `**基于JavaWeb的学生成绩管理系统**                                  |
| - | - |
|**项目负责人**|`              `**王梓齐****                            |
|**项目开发单位**|**江苏海洋大学应用技术学院计算机4241第3项目组**  |
|**小组成员**|` `**张庆祥、蔡家璇、刘川东、沈磊、程学峰、徐浩翔**                |







**2025年6月4日**


<a name="_toc296723290"></a><a name="_toc360453643"></a><a name="_toc422865760"></a><a name="_toc296256684"></a><a name="_toc296255961"></a><a name="_toc327852951"></a><a name="_toc296423709"></a><a name="_toc359946602"></a><a name="_toc359946393"></a><a name="_toc297035846"></a>软件工程课程设计项目组任务分派单（组长用）

<a name="_toc517672474"></a><a name="_toc517770659"></a>班级：  计算机4241  组别：  3   组长姓名：   王梓齐    时间： 2025年 6月 4日

项目名称：      **基于JavaWeb的学生成绩管理系统**      阶段名称：    项目计划分析    

|序号|学号|姓名|任务名称|具体任务内容|完成标准|起止日期|验收成绩|
| :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
|1|2024140518|王梓齐|分配计划|完成实施计划模块|计划分配合理|2025\.6.3-2025.6.4|80|
|2|2024140525|张庆祥|制作目录|根据项目计划书中的内容，生成目录|保证目录的正确及完整性|2025\.6.3-2025.6.4|80|
|3|2024140485|蔡家璇|引言部分编写|完成项目计划书中引言部分内容|计划书中背景、相关技术定义等描述完整|2025\.6.3-2025.6.4|80|
|4|2024140499|刘川东|分析本项目所需的相关条件|分析相关条件并编写计划书|条件分析，考虑周到|2025\.6.3-2025.6.4|80|
|5|2024140508|沈磊|项目产品概述、验收标准|描述相关项目的功能、验收标准|功能描述完整|2025\.6.3-2025.6.4|80|
|6|2024140486|程学峰|制定项目计划参加的人员及工作内容|编写计划书相关内容，确定相关人员参与|内容、人员合理无误|2025\.6.3-2025.6.4|80|
|7|2024140520|徐浩翔|描述本项目计划的要点等|编写计划书|要点描述完全|2025\.6.3-2025.6.4|80|

1. 本表由组长为其组员每次上机实践分派任务使用，应认真填写相关任务名称、内容、完成标准等信息；

2、本表在每次任务完成后，由组长按照完成标准验收，并给出每个组员成绩评定（每人平均70分制），除组长保留一份外，应及时上报至对分易。

注意：为组员分配颜色标志在上表里



# 目录
[1引言	1](#_toc5484)

[1.1 编写目的	1](#_toc25680)

[1.2 背景	1](#_toc7452)

[1.3 定义	1](#_toc2432)

[1.4 参考资料	2](#_toc10860)

[2 项目概述	2](#_toc22029)

[2.1 工作内容	2](#_toc9702)

[2.2 主要参加人员	2](#_toc1113)

[2.3 产品及成果	2](#_toc27772)

[2.3.1 程序	2](#_toc8532)

[2.3.2 文件	3](#_toc5110)

[2.3.3 服务	3](#_toc15804)

[2.3.4 非移交产品	3](#_toc23506)

[2.4 验收标准	3](#_toc3593)

[2.5 完成项目的最迟期限	4](#_toc30189)

[2.6 本计划的批准者和批准日期	4](#_toc5851)

[3 实施总计划	4](#_toc28081)

[3.1 工作任务的分解	4](#_toc12796)

[3.2 接口人员	7](#_toc27050)

[3.3 进度	7](#_toc22562)

[3.4 预算	8](#_toc8866)

[3.5 关键问题	8](#_toc15898)

[4 支持条件	9](#_toc1205)

[4.1 计算机系统支持	9](#_toc2060)

[4.2 需要用户承担的工作	9](#_toc20994)

[4.3 需由外单位提供的条件	10](#_toc11237)

[5 专题计划要点	10](#_toc7134)
#











**项目开发计划** 
`  `编制项目开发计划的目的是用文件的形式，把对于在开发过程中各项工作的负责人员、开发进度、 所需经费预算、所需软、硬件条件等问题作出的安排记载下来，以便根据本计划开展和检查本项目的开 发工作。编制内容要求如下：


<a name="_toc5484"></a>1引言
## <a name="_toc25680"></a>1.1 编写目的
本文档旨在详细描述“基于JavaWeb的学生成绩管理系统”的开发计划，为项目组成员提供明确的指导，确保项目按时、高质量完成。本文档的预期读者包括：项目负责人、项目组成员、指导老师。
## <a name="_toc7452"></a>1.2 背景
- **软件系统名称：** 基于JavaWeb的学生成绩管理系统
- **任务提出者：** 江苏海洋大学计算机工程学院
- **开发者：**计算机4241班第3项目组
- **用户：** 江苏海洋大学教师、学生、管理人员
- **网络环境：**依托江苏海洋大学校园网运行
- **与其他系统的关系：** 本系统为独立系统，暂不与其他系统直接交互，但预留接口，未来可与学校教务系统对接。
## <a name="_toc2432"></a>1.3 定义
- **JavaWeb：**集成 Java 技术与 Web 开发，构建动态网站与网络应用，融合 Java 语言、Servlet、JSP 等技术与 HTML、CSS、JavaScript 等 Web 技术。
- **Servlet：** Java Servlet 是 Java 编程技术，用于构建动态 Web 页面。
- **JSP：** JavaServer Pages，基于Java的服务器页面技术，用于简化动态网页的创建。
- **MySQL：** 一种流行的关系型数据库管理系统。
- **JDBC：** Java数据库连接，用于Java程序连接数据库的标准API。
- **IDEA：** IntelliJ IDEA，一种Java集成开发环境。
- **Tomcat：** 一种流行的Java Servlet容器。
- **Springboot:** 用于简化 Spring 应用开发的框架。
## <a name="_toc10860"></a>1.4 参考资料
- 《JSP+Servlet+Tomcat应用开发从零开始学(第二版)》
- 《网页设计与开发（第四版）》
- 《基于JavaWeb 技术的网上书城的设计与实现》
- 《Mysql数据库应用从入门到精通（第二版）》
- 本项目的需求规格说明书
- 软件工程相关标准
# <a name="_toc22029"></a>2 项目概述
## <a name="_toc9702"></a>2.1 工作内容
本项目涵盖需求分析、系统设计、编码实现、系统测试、系统部署与文档编写六大模块。需求分析聚焦用户需求挖掘与整理；系统设计规划系统架构、数据库结构、功能模块划分及界面布局；编码实现依托 JavaWeb 技术栈将设计蓝图转化为实际可运行的系统模块；系统测试全面检验系统功能完整性、性能稳定性及安全性；系统部署确保系统在校园网环境下稳定运行；文档编写则产出项目开发计划、需求规格说明书、设计文档等完整文档体系。
## <a name="_toc1113"></a>2.2 主要参加人员
项目开发团队由以下人员组成：

1. **项目负责人**：王梓齐，负责项目的整体规划、组织、协调和管理。
1. **开发人员**：张庆祥、蔡家璇、刘川东、沈磊、程学峰、徐浩翔，负责系统的设计、编码、测试等工作。

团队成员均具备一定的JavaWeb开发经验，熟悉Servlet、JSP、MySQL等技术，能够胜任本项目的开发工作。
## <a name="_toc27772"></a>2.3 产品及成果
### <a name="_toc8532"></a>2.3.1 程序
- **程序名称：** 学生成绩管理系统
- **编程语言：** Java
- **存储媒体：** 磁盘

**该程序的相关功能如下：**

1. **用户管理：管理员可以对用户信息进行增删改查**
1. **学生管理：管理员可以对学生信息进行增删改查**
1. **教师管理：管理员可以对教师信息进行增删改查**
1. **课程管理：管理员可以对课程信息进行增删改查**
1. **成绩管理：教师可以录入、修改学生成绩，学生可以查询自己的成绩**
1. **统计分析：系统可以对学生成绩进行统计分析，生成报表**
### <a name="_toc5110"></a>2.3.2 文件
- **项目开发计划：** 本文档，描述项目的整体规划。
- **需求规格说明书：** 详细描述系统的功能需求、性能需求、用户界面需求等。
- **设计文档：** 描述系统的架构设计、数据库设计、模块设计、用户界面设计等。
- **测试报告：** 记录系统的测试过程、测试结果、Bug修复情况等。
- **用户手册：** 指导用户如何使用系统。
- **数据库设计文档：** 数据库表结构设计文档，SQL脚本。
### <a name="_toc15804"></a>2.3.3 服务
- **培训：** 向用户提供系统使用培训，帮助用户快速上手。
- **安装：** 协助用户安装部署系统。
- **维护：** 提供系统维护服务，包括Bug修复、性能优化、安全加固等。
- **运行支持：** 提供系统运行支持，解决用户在使用过程中遇到的问题。
### <a name="_toc23506"></a>2.3.4 非移交产品
- **项目源代码：** 保存在项目组版本控制系统中，不向用户移交。
- **开发过程中产生的中间文档：** 如会议记录、草稿等。
## <a name="_toc3593"></a>2.4 验收标准
- **程序：**
  - 所有功能模块均能正常运行。
  - 系统性能满足用户需求。
  - 系统安全性符合安全标准。
  - 代码质量符合编码规范。
- **文件：**
  - 文档内容完整、准确、清晰。
  - 文档格式符合规范。
- **服务：**
  - 培训效果良好，用户能够熟练使用系统。
  - 安装部署顺利完成。
  - 维护服务及时响应用户需求。

**具体验收标准：**参照需求规格说明书、设计文档、测试报告。
## <a name="_toc30189"></a>2.5 完成项目的最迟期限
项目计划于2025年6月27日完成
## <a name="_toc5851"></a>2.6 本计划的批准者和批准日期
- **审查者：** 樊宁老师
- **批准者：**王梓齐
- **批准日期：** 2025年6月4日
# <a name="_toc28081"></a>3 实施总计划
## <a name="_toc12796"></a>3.1 工作任务的分解

|**任务编号**|**任务名称**|**负责人**|**参与人员**|**预定开始日期**|**完成日期**|**完成标志**|
| :- | :- | :- | :- | :- | :- | :- |
|1|需求分析|王梓齐|王梓齐、张庆祥、蔡家璇|2025/6/6|20256/13|完成需求规格说明书|
|2|系统设计|刘川东|刘川东、张庆祥、沈磊|2025/6/13|2025/6/16|完成系统设计文档、数据库设计文档|
|3|数据库实现|王梓齐|王梓齐、蔡家璇、沈磊|2025/6/16|2025/6/16|完成数据库脚本|
|4|用户登录模块开发|蔡家璇|||||

|蔡家璇、刘川东、程学峰||
| :- | :- |

|||||2025/6/17|2025/6/19|完成登录、注册、忘记密码功能|
| :- | :- | :- | :- | :- | :- | :- |
|5|管理员模块开发||||||

|张庆祥||
| :- | :- |

||||沈磊、徐浩翔|2025/6/19|2025/6/21|完成管理员对用户、学生、教师、课程的管理|
| :- | :- | :- | :- | :- | :- | :- |
|6|学生模块开发|程学峰|程学峰、徐浩翔|2025/6/21|2025/6/23|完成学生查询成绩功能|
|7|教师模块开发|沈磊|王梓齐、蔡家璇|2025/6/23|2025/6/25|完成教师管理学生信息、成绩功能|
|8|系统测试|徐浩翔|所有成员|2025/6/25|2025/6/27|完成测试报告|
|9|文档编写|王梓齐|所有成员|2025/6/27|2025/6/27|完成用户手册、项目总结报告|

**详细任务分解：**

- **需求分析：**
  - 与用户沟通，收集用户需求。
  - 编写需求规格说明书。
  - 需求评审。
- **系统设计：**
  - 设计系统架构，确定系统采用的技术栈。
  - 设计数据库结构，包括表名、字段、数据类型、索引等。
  - 设计模块划分，确定每个模块的功能和接口。
  - 设计用户界面，包括页面布局、控件、交互方式等。
  - 编写设计文档。
  - 设计评审。
- **编码实现：**
  - 根据设计文档，编写Java代码实现系统的各个模块。
  - 进行代码评审，确保代码质量。
  - 编写单元测试用例，对代码进行单元测试。
- **系统测试：**
  - 编写测试计划，确定测试范围、测试方法、测试用例等。
  - 进行功能测试，验证系统是否满足用户需求。
  - 进行性能测试，评估系统的性能指标。
  - 进行安全性测试，检查系统是否存在安全漏洞。
  - 编写测试报告。
- **系统部署：**
  - 配置Tomcat服务器。
  - 将系统部署到Tomcat服务器上。
  - 进行系统测试，确保系统能够正常运行。
- **文档编写：**
  - 编写用户手册，指导用户如何使用系统。
  - 编写项目总结报告，总结项目的经验教训。
## <a name="_toc27050"></a>3.2 接口人员
- **用户接口人：** 王梓齐，负责与用户沟通，收集用户需求，反馈项目进展。
- **指导教师接口人：** 王梓齐，负责与指导教师沟通，汇报项目进展，接受指导。
- **质量管理接口人：** 王梓齐，负责代码评审、测试、质量保证等工作。
## <a name="_toc22562"></a>3.3 进度

|**任务名称**|**预定开始日期**|**完成日期**|**所需资源**|**完成标志**|
| :- | :- | :- | :- | :- |
|需求分析|2025/6/6|20256/13|项目组成员、需求分析工具|完成需求规格说明书|
|系统设计|2025/6/13|2025/6/16|项目组成员、UML建模工具、数据库设计工具|完成系统设计文档、数据库设计文档|
|数据库实现|2025/6/16|2025/6/16|项目组成员、MySQL数据库|完成数据库脚本|
|用户登录模块开发|2025/6/17|2025/6/19|项目组成员、IDEA开发工具、Tomcat服务器|完成登录、注册、忘记密码功能|
|管理员模块开发|2025/6/19|2025/6/21|项目组成员、IDEA开发工具、Tomcat服务器|完成管理员对用户、学生、教师、课程的管理|
|学生模块开发|2025/6/21|2025/6/23|项目组成员、IDEA开发工具、Tomcat服务器|完成学生查询成绩功能|
|教师模块开发|2025/6/23|2025/6/25|项目组成员、IDEA开发工具、Tomcat服务器|完成教师管理学生信息、成绩功能|
|系统测试|2025/6/25|2025/6/27|项目组成员、测试工具、测试环境|完成测试报告|
|文档编写|2025/6/27|2025/6/27|项目组成员、文档编辑工具|完成用户手册、项目总结报告|

**里程碑事件：**

- 2025年6月13日：完成需求规格说明书
- 2025年6月16日：完成系统设计文档、数据库设计文档
- 2025年6月19日：完成用户登录模块
- 2025年6月21日：完成管理员模块
- 2025年6月25日：完成学生模块、教师模块
- 2025年6月27日：完成系统测试、文档编写
## <a name="_toc8866"></a>3.4 预算

|**费用项目**|**预算金额（元）**|**来源**|
| :- | :- | :- |
|办公费|200|自筹|
|差旅费|100|自筹|
|机时费|0|实验室提供|
|资料费|100|自筹|
|通讯设备租金|0|自有设备|
|专用设备租金|0|实验室提供|
|**总计**|**400**||
## <a name="_toc15898"></a>3.5 关键问题
- **技术难点：**
  - 如何保证系统的安全性，防止数据泄露和非法访问。
  - 如何优化系统的性能，提高响应速度。
  - 如何设计用户友好的界面，提高用户体验。
- **风险：**
  - 需求变更导致项目延期。
  - 技术难题无法解决导致项目失败。
  - 成员退出导致项目人手不足。
- **应对措施：**
  - 加强需求管理，严格控制需求变更。
  - 提前进行技术调研，做好技术储备。
  - 建立良好沟通机制，及时发现和解决问题。
  - 备份项目成员，确保项目人手充足。
# <a name="_toc1205"></a>4 支持条件
## <a name="_toc2060"></a>4.1 计算机系统支持

|**设备名称**|**数量**|**到货日期**|**使用时间**|
| :- | :- | :- | :- |
|计算机|7|已有|全程|
|操作系统|7|已有|全程|
|开发工具IDEA|7|已有|全程|
|Tomcat服务器|1|已有|全程|
|MySQL数据库|1|已有|全程|
## <a name="_toc20994"></a>4.2 需要用户承担的工作
- 提供需求反馈，及时响应项目组提出的问题。
- 参与系统测试，提供测试数据和测试意见。
- 接受系统培训，学习系统使用方法。

**完成期限：** 贯穿项目整个过程。
## <a name="_toc11237"></a>4.3 需由外单位提供的条件
无
# <a name="_toc7134"></a>5 专题计划要点
- **测试计划：** 详细描述测试范围、测试方法、测试用例、测试环境、测试人员等。
- **安全保密计划：** 描述如何保护系统的数据安全，防止数据泄露和非法访问。
- **质量保证计划：** 描述如何保证系统的质量，包括代码评审、测试、文档编写等。
- **用户培训计划：** 描述如何向用户提供系统使用培训，帮助用户快速上手。
- **配置管理计划：** 使用Git进行代码管理，进行版本控制。



10
![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAcCAYAAACtQ6WLAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAhSURBVDhPYxhg8B9KYwCQBE5JEBg6kiAJGB4FQxUwMAAA/ZAL9aEGFGMAAAAASUVORK5CYII=)
