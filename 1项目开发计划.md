文档编号：**JOU-SECD-RJ171-01T-01**

版 本 号：**V1.0**
## <a name="_toc296255960"></a><a name="_toc359946392"></a><a name="_toc296723289"></a><a name="_toc296256683"></a><a name="_toc297035845"></a><a name="_toc422865759"></a><a name="_toc360453642"></a><a name="_toc327852950"></a><a name="_toc359946601"></a><a name="_toc296423708"></a>   <a name="_toc517672473"></a><a name="_toc517770658"></a>\*\*\*\*系统项目开发计划说明书

|**项目名称**|`                 `**\*\*\*\*系统**                    |
| - | - |
|**项目负责人**|`                    `**\*\*\***                        |
|**项目开发单位**|**江苏海洋大学计算机工程学院软件171班第1项目组**  |
|**小组成员**|`        `**\*\*、\*\*、\*\*、\*\*、\*\*、\*\***                |







**2020年6月22日**




<a name="_toc359946393"></a><a name="_toc296423709"></a><a name="_toc296255961"></a><a name="_toc296723290"></a><a name="_toc297035846"></a><a name="_toc296256684"></a><a name="_toc327852951"></a><a name="_toc360453643"></a><a name="_toc422865760"></a><a name="_toc359946602"></a>软件工程课程设计项目组任务分派单（组长用）

<a name="_toc517672474"></a><a name="_toc517770659"></a>班级：  软件171班  组别：  1   组长姓名：    \*\*     时间： 2020年 6月 22日

项目名称：        \*\*系统                           阶段名称：    项目计划分析    

|序号|学号|姓名|任务名称|具体任务内容|完成标准|起止日期|验收成绩|
| :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
|1|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|2|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|3|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|4|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|5|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|6|2017\*\*\*\*\*\*|\*\*|||||\*\*|

1. 本表由组长为其组员每次上机实践分派任务使用，应认真填写相关任务名称、内容、完成标准等信息；

2、本表在每次任务完成后，由组长按照完成标准验收，并给出每个组员成绩评定（每人平均70分制），除组长保留一份外，应及时上报至对分易。

注意：为组员分配颜色标志在上表里



# 目录
# （根据内容生成目录）
[1 引言	1](#_toc517770660)

[1.1编写目的	1](#_toc517770661)

[1.2背景	1](#_toc517770662)

[1.3定义	2](#_toc517770663)

[1.4参考资料	2](#_toc517770664)

[2 项目概述	2](#_toc517770665)

[2.1工作内容	2](#_toc517770666)

[2.2 主要参与人员	3](#_toc517770667)

[2.3 产品及成果	3](#_toc517770668)

[2.3.1程序	3](#_toc517770669)

[2.3.2文件	3](#_toc517770670)

[2.3.3服务	4](#_toc517770671)

[2.3.4非移交产品	4](#_toc517770672)

[2.4 验收标准	4](#_toc517770673)

[2.5完成项目的最迟期限	5](#_toc517770674)

[2.6 本计划的批准者和批准日期	5](#_toc517770675)

[3 实施总计划	5](#_toc517770676)

[3.1工作任务的分解	5](#_toc517770677)

[3.2接口人员	10](#_toc517770678)

[3.3进度	10](#_toc517770679)

[3.4预算	11](#_toc517770680)

[3.5关键问题	11](#_toc517770681)

[4 支持条件	12](#_toc517770682)

[4.1计算机系统支持	12](#_toc517770683)

[4.2需要用户承担的工作	12](#_toc517770684)

[4.3需要外单位提供的条件	13](#_toc517770685)

[5专题计划要点	13](#_toc517770686)



**1 项目开发计划** 
`  `编制项目开发计划的目的是用文件的形式，把对于在开发过程中各项工作的负责人员、开发进度、 所需经费预算、所需软、硬件条件等问题作出的安排记载下来，以便根据本计划开展和检查本项目的开 发工作。编制内容要求如下：
`  `**1**．**1引言**
`  `1．1．1编写目的
`  `1．1．2背景
`  `1．1．3定义
`  `1．1．4参考资料 
`  `**1．2项目概述**
`  `1．2．1作内容
`  `1．2．2主要参加人员
`  `1．2．3产品及成果 
`  `1．2．3．1程序 
`  `1．2．3．2文件
`  `1．2．3．3服务 
`  `1．2．3．4非移交产品
`  `1．2．4验收标准 
`  `1．．2．5完成项目的最迟期限 
`  `1．2．6本计划的审查者与批准者 
`  `**1．3实施总计划**
`  `1．3．1工作任务的分解 
`  `1．3．2接口人员
`  `1．3．3进度
`  `1．3．4预算
`  `1．3．5关键问题
`  `**1．4支持条件** 
`  `1．4．1计算机系统支持
`  `1．4．2需要用户承担的工作
`  `1．4．3需由外单位提供的条件
`  `**1．5专题计划要点** 




**附录B
项目开发计划的编写提示** 


**B．1引言**
` `**B．1．1编写目的** 
`   `说明编写这份项目开发计划的目的，并指出预期的读者。
` `**B．1．2背景**
`   `说明：
`   `a．待开发的软件系统的名称；
`   `b．本项目的任务提出者、开发者、用户及实现该软件的计算中心或计算机网络；
`   `C．该软件系统同其他系统或其他机构的基本的相互来往关系。
` `**B．1．3定义** 
`   `列出本文件中用到的专门术语的定义和外文首字母组词的原词组。
` `**B．1．4参考资料**
`   `列出用得着的参考资料，如：
`   `a．本项目的经核准的计划任务书或合同、上级机关的批文；
`   `b．属于本项目的其他已发表的文件；
`   `C．本文件中各处引用的文件、资料，包括所要用到的软件开发标准。 列出这些文件资料的标题、文件编号、发表日期和出版单位，说明能够得到这些文件资料的来源。

**B．2项目概述 
`  `B．2．1 工作内容**
`   `简要地说明在本项目的开发中须进行的各项主要工作。
`  `**B．2．2主要参加人员**
`   `扼要说明参加本项目开发工作的主要人员的情况，包括他们的技术水平。
`  `**B．2．3产品**
`  `B．2．31程序
`   `列出需移交给用户的程序的名称、所用的编程语言及存储程序的媒体形式，并通过引用有关文件， 逐项说明其功能和能力。 
`  `B．2．3．2文件
`   `列出需移交给用户的每种文件的名称及内容要点。 
`  `B．2．3．3服务
`   `列出需向用户提供的各项服务，如培训安装、维护和运行支持等，应逐项规定开始日期、所提供支持 的级别和服务的期限。
`  `B．2．3．4非移交的产品 
`   `说明开发集体应向本单位交出但不必向用户移交的产品（文件甚至某些程序）。
`  `**B．2．4验收标准** 
`   `对于上述这些应交出的产品和服务，逐项说明或引用资料说明验收标准。
`  `**B．2．5完成项目的员迟用限 
`  `B．2．6本计划的批准者和批准日期** 

**B．3实施计划** 
`  `**B．3．1工作任务的分门与人员分工** 
`   `对于项目开发中需完成的各项工作，从需求分析、设计、实现、测试直到维护，包括文件的编制、审批、打印、分发工作，用户培训工作，软件安装工作等，按层次进行分解，指明每项任务的负责人和参加人员。
`  `**B．3．2 接口人员**
`    `说明负责接口工作的人员及他们的职责，包括：
`    `a ．负责本项目同用户的接口人员；
`    `b．负责本项目同本单位各管理机构，如合同计划管理部门、财务部门、质量管理部门等的接口人员；   
`    `c．负责本项目同各分合同负责单位的接口人员等。 
`  `**B．3．3进度** 
`    `对于需求分析、设计、编码实现、测试、移交、培训和安装等工作，给出每项工作任务的预。定开始日期、完成日期及所需资源，规定各项工作任务完成的先后顺序以及表征每项工作任务完成的标志性事件（即所谓“里程碑”）。 
`  `**B．3．4预算** 
`    `逐项列出本开发项目所需要的劳务（包括人员的数量和时间）以及经费的预算（包括办公费、差旅费、机时费、资料费、通讯设备和专用设备的租金等）和来源。
`  `**B．3．5关键问题**
`     `逐项列出能够影响整个项目成败的关键问题、技术难点和风险，指出这些问题对项目的影响。

**B．4支持条件**
`  `说明为支持本项目的开发所需要的各种条件和设施。
`  `**B．4．1计算机系统支持**
`     `逐项列出开发中和运行时所需的计算机系统支持，包括计算机、外围设备、通讯设备、模拟器、编译 （或 汇编）程序、操作系统、数据管理程序包、数据存储能力和测试支持能力等，逐项给出有关到货日期、 使用时间的要求。
`  `**B．4．2需由用户承担的工作**
`     `逐项列出需要用户承担的工作和完成期限。包括需由用户提供的条件及提供时间。
`  `**B．4．3由外单位提供的条件** 
`     `逐项列出需要外单位分合同承包者承担的工作和完成的时间，包括需要由外单位提供的条件和提 供的时间。 

**B．5专题计划要点**
`  `说明本项目开发中需制订的各个专题计划（如分合同计划、开发人员培训计划、测试计划、安全保密 计划、质量保证计划、配置管理计划、用户培训计划、系统安装计划等）的要点。



