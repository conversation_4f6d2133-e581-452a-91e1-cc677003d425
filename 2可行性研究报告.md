文档编号：**JOU-SECD-RJ171-01T-02**

版 本 号：**V1.0**
## <a name="_toc296723289"></a><a name="_toc296423708"></a><a name="_toc296256683"></a><a name="_toc360453642"></a><a name="_toc359946601"></a><a name="_toc422865759"></a><a name="_toc297035845"></a><a name="_toc296255960"></a><a name="_toc327852950"></a><a name="_toc359946392"></a>   <a name="_toc517770658"></a><a name="_toc517672473"></a>\*\*\*\*系统可行性研究报告

|**项目名称**|`                 `**\*\*\*\*系统**                    |
| - | - |
|**项目负责人**|`                    `**\*\*\***                        |
|**项目开发单位**|**江苏海洋大学计算机工程学院软件171班第1项目组**  |
|**小组成员**|`        `**\*\*、\*\*、\*\*、\*\*、\*\*、\*\***                |







**2020年6月22日**
**


<a name="_toc360453643"></a><a name="_toc296723290"></a><a name="_toc422865760"></a><a name="_toc296423709"></a><a name="_toc296255961"></a><a name="_toc297035846"></a><a name="_toc359946602"></a><a name="_toc296256684"></a><a name="_toc327852951"></a><a name="_toc359946393"></a>软件工程课程设计项目组任务分派单（组长用）

<a name="_toc517672474"></a><a name="_toc517770659"></a>班级：  软件171班  组别：  1   组长姓名：    \*\*     时间： 2020年 6月 22日

项目名称：        \*\*系统                           阶段名称：    可行性研究    

|序号|学号|姓名|任务名称|具体任务内容|完成标准|起止日期|验收成绩|
| :-: | :-: | :-: | :-: | :-: | :-: | :-: | :-: |
|1|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|2|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|3|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|4|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|5|2017\*\*\*\*\*\*|\*\*|||||\*\*|
|6|2017\*\*\*\*\*\*|\*\*|||||\*\*|

1. 本表由组长为其组员每次上机实践分派任务使用，应认真填写相关任务名称、内容、完成标准等信息；

2、本表在每次任务完成后，由组长按照完成标准验收，并给出每个组员成绩评定（每人平均70分制），除组长保留一份外，应及时上报至对分易。

注意：为组员分配颜色标志在上表里



# 目录
# （根据内容生成二级目录）


**


**7 可行性研究报告** 
`    `可行性研究报告的编写目的是：说明该软件开发项目的实现在技术、经济和社会条件方面的可行 性；评述为了合理地达到开发目标而可能选择的各种方案；说明并论证所选定的方案。
`    `可行性研究报告的编写内容要求如下：
` `**7．1引言**
` `7．1C1编写目的 
` `7．1．2背景 
` `7．1．3定义 
` `7．1．4参考资料 7
` `**7．2可行性研究的前提**
` `7．2．1要求
` `7．2．2目标 
` `7．2．3条件、假定和限制
` `7．2．4进行可行性研究的方法
` `7．2．5评价尺度 
` `**7．3对现有系统的分析** 
` `7．3．1数据流程和处理流程
` `7．3．2工作负荷 
` `7．3．3费用开支
` `7．3．4人员 
` `7．3．5设备 
` `7．3．6局限性 
` `**7．4所建议的系统** 
` `7．4．1对所建议系统的说明 
` `7．4．2数据流程和处理流程 
` `7．4．3改进之处 
` `7．4．4影响 
` `7．4．4．1对设备的影响 
` `7．4．4．2对软件的影响 
` `7．4．4．3对用户单位机构的影响
` `7．4．4．4对系统运行的影响
` `7．4．4．5对开发的影响
` `7．4，4．6对地点和设施的影响 
` `7．4．4．7对经费开支的影响 
` `7．4．5局限性 
` `7．4．6技术条件方面的可行性 
` `**7．5可选择的其他系统方案**
` `7．5．1可选择的系统方案1
` `7．5．2可选择的系统方案2
`    `．．．．．．
` `**7．6投资及收益分析** 
` `7．6．1支出 
` `7．6．1．1基本建设投资 
` `7．6．1．2其他一次性支出 
` `7．6．1．3非一次性支出 
` `7．6．2收益 
` `7．6，2．1一次性收益
` `7．6．2．2非一次性收益 
` `7．6．2．3不可定量的收益
` `7．6．3收益／投资比 
` `7．6．4投资回收周期 
` `7．6．5敏感性分析
` `**7．7社会条件方面的可行性**
` `7．7．1法律方面的可行性
` `7．7．2使用方面的可行性 
` `**7．8结论**

**可行性研究报告的编写提示**
（参考件） 
**A．1引言 
`  `A．1．1编写目的** 
说明编写本可行性研究报告的目的，指出预期的读者。
`  `**A．1．2背景** 
`     `说明：
`     `a．所建议开发的软件系统的名称；
`     `b．本项目的任务提出者、开发者、用户及实现该软件的计算中心或计算机网络；
`     `C．该软件系统同其他系统或其他机构的基本的相互来往关系。
` `**A．1．3定义** 
`     `列出本文件中用到的专门术语的定义和外文首字母组词的原词组。
` `**A．1．4参考资料** 
`    `列出用得着的参考资料，如：
`    `a．本项目的经核准的计划任务书或合同、上级机关的批文；
`    `b．属于本项目的其他已发表的文件；
`    `C．本文件中各处引用的文件、资料，包括所需用到的软件开发标准。|
`    `列出这些文件资料的标题、文件编号、发表日期和出版单位，说明能够得到这些文件资料的来源。


**A．2可行性研究的前提**
`    `说明对所建议的开发项目进行可行性研究的前提，如要求、目标、假定、限制等。
`  `**A．2．1要求**
`    `说明对所建议开发的软件的基本要求，如：
`    `a．功能；
`    `b．性能；
`    `C．输出如报告、文件或数据，对每项输出要说明其特征，如用途、产生频度、接口以及分发对象；      
`    `d．输入说明系统的输入，包括数据的来源、类型、数量、数据的组织以及提供的频度；
`    `e．处理流程和数据流程用图表的方式表示出最基本的数据流程和处理流程，并辅之以叙述；
`    `f．在安全与保密方面的要求；
`    `g．同本系统相连接的其他系统；
`    `h．完成期限。
`  `**A．2**．**2目标** 
`    `说明所建议系统的主要开发目标，如：
`    `a．人力与设备费用的减少；
`    `b．处理速度的提高；
`    `C．控制精度或生产能力的提高；
`    `d．管理信息服务的改进；
`    `e．自动决策系统的改进；
`    `f．人员利用率的改进。 
`  `**A．2．3条件、假定和限制**
`    `说明对这项开发中给出的条件、假定和所受到的限制，如：
`    `a．所建议系统的运行寿命的最小值；
`    `b．进行系统方案选择比较的时间； 
`    `c．经费、投资方面的来源和限制； 
`    `d．法律和政策方面的限制；
`    `e．硬件、软件、运行环境和开发环境方面的条件和限制；
`    `f．可利用的信息和资源；
`    `g**．**系统投入使用的最晚时间。
`  `**A．2．4进行可行性研究的方法** 
`    `说明这项可行性研究将是如何进行的，所建议的系统将是如何评价的。摘要说明所使用的基本方法 和策略，如调查、加权、确定模型、建立基准点或仿真等。
`  `**A．2．5评价尺度**
`    `说明对系统进行评价时所使用的主要尺度，如费用的多少、各项功能的优先次序、开发时间的长短 及使用中的难易程度。

**A．3 对现有系统的分析**
`    `这里的现有系统是指当前实际使用的系统，这个系统可能是计算机系统，也可能是一个机械系统甚 至是一个人工系统。
`    `分析现有系统的目的是为了进一步阐明建议中的开发新系统或修改现有系统的必要性。 
` `**A．3**．**1处理流程和数据流程**
`    `说明现有系统的基本的处理流程和数据流程。此流程可用图表即流程图的形式表示，并加以叙述。
` `**A．3．2工作负荷** 
`    `列出现有系统所承担的工作及工作量。
` `**A．3**．**3费用开支**
`    `列出由于运行现有系统所引起的费用开支，如人力、设备、空间、支持性服务、材料等项开支以及开 支总额。
` `**A．3．4人员** 
`    `列出为了现有系统的运行和维护所需要的人员的专业技术类别和数量。
` `**A．3．5设备** 
`    `列出现有系统所使用的各种设备。
` `**A．3．6局限性**
`    `列出本系统的主要的局限性，例如处理时间赶不上需要，响应不及时，数据存储能力不足，处理功能 不够等。并且要说明，为什么对现有系统的改进性维护已经不能解决问题。

**A．4 所建议的系统**
`    `本章将用来说明所建议系统的目标和要求将如何被满足。
` `**A．4．1对所建议系统的说明**
`    `概括地说明所建议系统，并说明在第A．2章中列出的那些要求将如何得到满足，说明所使用的基本 方法及理论根据。 
` `**A．4．2处理流程和数据流程**
`    `给出所建议系统的处理流程和数据流程。
` `**A．4．3改进之处** 
`    `按A．2．2条中列出的目标，逐项说明所建议系统相对于现存系统具有的改进。
` `**A．4．4影响**
`    `说明在建立所建议系统时，预期将带来的影响，包括：
` `A．4．4．1对设备的影响 
`    `说明新提出的设备要求及对现存系统中尚可使用的设备须作出的修改。
` `A．4．4．2对软件的影响 
`    `说明为了使现存的应用软件和支持软件能够同所建议系统相适应。而需要对这些软件所进行的修 改和补充。
` `A．4．4．3对用户单位机构的影响 
`    `说明为了建立和运行所建议系统，对用户单位机构、人员的数量和技术水平等方面的全部要求。
` `A． 4． 4． 4对系统运行过程的影响 
`    `说明所建议系统对运行过程的影响，如： 
`    `a．用户的操作规程；
`    `b．运行中心的操作规程； 
`    `C．运行中心与用户之间的关系；
`    `d．源数据的处理； 
`    `e．数据进入系统的过程；
`    `f．对数据保存的要求，对数据存储、恢复的处理；
`    `g．输出报告的处理过程、存储媒体和调度方法；
`    `h．系统失效的后果及恢复的处理办法。 
` `A．4．4．5对开发的影响 
`    `说明对开发的影响，如：
`    `a．为了支持所建议系统的开发，用户需进行的工作；
`    `b．为了建立一个数据库所要求的数据资源；
`    `C．为了开发和测验所建议系统而需要的计算机资源；
`    `d．所涉及的保密与安全问题。
`  `A．4．4．6对地点和设施的影响 
`    `说明对建筑物改造的要求及对环境设施的要求。
`  `A．4．4．7对经费开支的影响 
`    `扼要说明为了所建议系统的开发，设计和维持运行而需要的各项经费开支。
`  `**A．4．5局限性** 
`    `说明所建议系统尚存在的局限性以．及这些问题未能消除的原因。
`  `**A．4．6技术条件方面的可行性** 
`    `本节应说明技术条件方面的可行性，如：
`    `a．在当前的限制条件下，该系统的功能目标能否达到；
`    `b．利用现有的技术，该系统的功能能否实现；
`    `C．对开发人员的数量和质量的要求并说明这些要求能否满足；
`    `d．在规定的期限内，本系统的开发能否完成。

**A．5可选择的其他系统方案** 
`   `扼要说明曾考虑过的每一种可选择的系统方案，包括需开发的和可从国内国外直接购买的，如果没 有供选择的系统方案可考虑，则说明这一点。 
` `**A．5．1可选择的系统方案1** 
`   `参照第A．4章的提纲，说明可选择的系统方案1，并说明它未被选中的理由。
` `**A．5．2可选择的系统方案2** 
`   `按类似A． 5． 1条的方式说明第2个乃至第。个可选择的系统方案。
`    `．．．．．．

**A．6投资及效益分析** 
` `**A．6．1支出**
`   `对于所选择的方案，说明所需的费用。如果已有一个现存系统，则包括该系统继续运行期间所需的费用。 
` `A．6．1．1基本建设投资
`   `包括采购、开发和安装下列各项所需的费用，如： 
`   `a．房屋和设施；
`   `b． A DP设备； 
`   `C．数据通讯设备；
`   `d．环境保护设备；
`   `e．安全与保密设备；
`   `f．ADP操作系统的和应用的软件； 
`   `g．数据库管理软件。
` `A．6．1．2其他一次性支出
`   `包括下列各项所需的费用，如：
`   `a．研究（需求的研究和设计的研究）；
`   `b．开发计划与测量基准的研究；
`   `C．数据库的建立；
`   `d．ADP软件的转换；
`   `e．检查费用和技术管理性费用；
`   `f．培训费、旅差费以及开发安装人员所需要的一次性支出； 
`   `g．人员的退休及调动费用等。
` `A．6．1．3非一次性支出
`   `列出在该系统生命期内按月或按季或按年支出的用于运行和维护的费用，包括： 
`   `a．设备的租金和维护费用； 
`   `b软件的租金和维护费用；
`   `C．数据通讯方面的租金和维护费用；
`   `d．人员的工资、奖金；
`   `e．房屋、空间的使用开支；
`   `f．公用设施方面的开支；
`   `g．保密安全方面的开支； 
`   `h．其他经常性的支出等。
` `**A．6．2收益** 
`   `对于所选择的方案，说明能够带来的收益，这里所说的收益，表现为开支费用的减少或避免、差错的减少、灵活性的增加、动作速度的提高和管理计划方面的改进等，包括；
` `A．6．2．1一次性收益
`   `说明能够用人民币数目表示的一次性收益，可按数据处理、用户、管理和支持等项分类叙述，如：
`   `a．开支的缩减包括改进了的系统的运行所引起的开支缩减，如资源要求的减少，运行效率的改进，数据进入、存贮和恢复技术的改进，系统性能的可监控，软件的转换和优化，数据压缩技术的采用，处理的集中化／分布化等；
`   `b．价值的增升包括由于一个应用系统的使用价值的增升所引起的收益，如资源利用的改进，管理和运行效率的改进以及出错率的减少等；
`   `C．其他如从多余设备出售回收的收入等。
` `A．6．2．2非一次性收益 
`   `说明在整个系统生命期内由于运行所建议系统而导致的按月的、按年的能用人民币数目表示的收益，包括开支的减少和避免。 
` `A．6．2．3不可定量的收益 
`   `逐项列出无法直接用人民币表示的收益，如服务的改进，由操作失误引起的风险的减少，信息掌握情况的改进，组织机构给外界形象的改善等。有些不可捉摸的收益只能大概估计或进行极值估计（按最好和最差情况估计）。 
` `**A．6．3收益／投资比** 
`   `求出整个系统生命期的收益／投资比值。 
` `**A．6．4投资回收周期** 
`   `求出收益的累计数开始超过支出的累计数的时间。
` `**A．6．5敏感性分析**
`   `所谓敏感性分析是指一些关键性因素如系统生命期长度、系统的工作负荷量、工作负荷的类型与这些不同类型之间的合理搭配、处理速度要求、设备和软件的配置等变化时，对开支和收益的影响最灵敏的范围的估计。在敏感性分析的基础上做出的选择当然会比单一选择的结果要好一些。

**A．7 社会因素方面的可行性** 
`   `本章用来说明对社会因素方面的可行性分析的结果，包括：
` `**A．7．1法律方面的可行性** 
`   `法律方面的可行性问题很多，如合同责任、侵犯专利权、侵犯版权等方面的陷井，软件人员通常是不熟悉的，有可能陷入，务必要注意研究。
` `**A．7．2使用方面的可行性** 
`   `例如从用户单位的行政管理、工作制度等方面来看，是否能够使用该软件系统；从用户单位的工作人员的素质来看，是否能满足使用该软件系统的要求等等，都是要考虑的。

**A．8 结论**
`   `在进行可行性研究报告的编制时，必须有一个研究的结论。结论可以是：
`   `a．可以立即开始进行；
`   `b．需要推迟到某些条件（例如资金、人力、设备等）落实之后才能开始进行；
`   `c．需要对开发目标进行某些修改之后才能开始进行；
`   `d．不能进行或不必进行（例如因技术不成熟、经济上不合算等）。
